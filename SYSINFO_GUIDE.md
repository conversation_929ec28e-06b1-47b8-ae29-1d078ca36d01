# sysinfo 库使用指南

这是一个完整的 sysinfo 库学习指南，包含了所有常用 API 的使用示例。

## 🚀 快速开始

### 1. 运行基础教程
```bash
cargo run --example sysinfo_tutorial
```

### 2. 运行完整演示
```bash
cargo run sysinfo
```

### 3. 运行默认程序（Mac 序列号）
```bash
cargo run
```

## 📚 sysinfo 库核心概念

### 系统支持检查
```rust
use sysinfo::System;

// 检查当前系统是否被支持
if sysinfo::IS_SUPPORTED_SYSTEM {
    println!("系统被支持");
}
```

### 创建 System 实例的三种方式
```rust
// 1. 创建空实例（需要手动刷新）
let mut sys = System::new();

// 2. 创建并加载所有信息
let mut sys = System::new_all();

// 3. 创建并指定要加载的信息
let mut sys = System::new_with_specifics(
    RefreshKind::nothing()
        .with_memory(MemoryRefreshKind::everything())
        .with_cpu(CpuRefreshKind::everything())
);
```

## 🔧 主要功能模块

### 1. 系统基本信息
```rust
// 静态方法，不需要实例
println!("系统名称: {:?}", System::name());
println!("内核版本: {:?}", System::kernel_version());
println!("操作系统版本: {:?}", System::os_version());
println!("主机名: {:?}", System::host_name());
println!("CPU 架构: {}", System::cpu_arch());
println!("物理核心数: {:?}", System::physical_core_count());
```

### 2. 内存信息
```rust
let mut sys = System::new();
sys.refresh_memory(); // 刷新内存信息

println!("总内存: {} bytes", sys.total_memory());
println!("已用内存: {} bytes", sys.used_memory());
println!("可用内存: {} bytes", sys.available_memory());
println!("空闲内存: {} bytes", sys.free_memory());

// 交换分区
println!("总交换分区: {} bytes", sys.total_swap());
println!("已用交换分区: {} bytes", sys.used_swap());
```

### 3. CPU 信息
```rust
let mut sys = System::new();
sys.refresh_cpu_all(); // 刷新所有 CPU 信息

println!("CPU 核心数: {}", sys.cpus().len());
println!("全局 CPU 使用率: {:.1}%", sys.global_cpu_usage());

// 遍历每个 CPU 核心
for (i, cpu) in sys.cpus().iter().enumerate() {
    println!("CPU {}: {}", i, cpu.name());
    println!("  品牌: {}", cpu.brand());
    println!("  频率: {} MHz", cpu.frequency());
    println!("  使用率: {:.1}%", cpu.cpu_usage());
}
```

**重要提示**: CPU 使用率需要两次测量才能准确，建议在两次调用之间等待至少 `MINIMUM_CPU_UPDATE_INTERVAL`。

### 4. 进程信息
```rust
let mut sys = System::new();
sys.refresh_processes(ProcessesToUpdate::All, true);

println!("总进程数: {}", sys.processes().len());

// 遍历所有进程
for (pid, process) in sys.processes() {
    println!("PID: {}, 名称: {}", pid, process.name().to_string_lossy());
    println!("  内存: {} bytes", process.memory());
    println!("  CPU 使用率: {:.1}%", process.cpu_usage());
    println!("  状态: {:?}", process.status());
}

// 按名称查找进程
let rust_processes: Vec<_> = sys.processes_by_name("rust".as_ref()).collect();

// 获取特定进程
if let Some(process) = sys.process(Pid::from(1234)) {
    println!("找到进程: {}", process.name().to_string_lossy());
}

// 获取当前进程
if let Ok(current_pid) = sysinfo::get_current_pid() {
    if let Some(current_process) = sys.process(current_pid) {
        println!("当前进程: {}", current_process.name().to_string_lossy());
    }
}
```

### 5. 磁盘信息
```rust
use sysinfo::Disks;

let disks = Disks::new_with_refreshed_list();

for disk in &disks {
    println!("磁盘: {:?}", disk.name());
    println!("  挂载点: {:?}", disk.mount_point());
    println!("  文件系统: {:?}", disk.file_system());
    println!("  类型: {:?}", disk.kind());
    println!("  总容量: {} bytes", disk.total_space());
    println!("  可用空间: {} bytes", disk.available_space());
    println!("  是否可移除: {}", disk.is_removable());
}
```

### 6. 网络信息
```rust
use sysinfo::Networks;

let networks = Networks::new_with_refreshed_list();

for (interface_name, data) in &networks {
    println!("接口: {}", interface_name);
    println!("  MAC 地址: {}", data.mac_address());
    println!("  接收数据: {} bytes", data.total_received());
    println!("  发送数据: {} bytes", data.total_transmitted());
    println!("  接收包数: {}", data.total_packets_received());
    println!("  发送包数: {}", data.total_packets_transmitted());
}
```

### 7. 组件温度
```rust
use sysinfo::Components;

let components = Components::new_with_refreshed_list();

for component in &components {
    println!("组件: {}", component.label());
    println!("  当前温度: {:.1}°C", component.temperature());
    if let Some(max) = component.max() {
        println!("  最高温度: {:.1}°C", max);
    }
    if let Some(critical) = component.critical() {
        println!("  临界温度: {:.1}°C", critical);
    }
}
```

### 8. 用户和组信息
```rust
use sysinfo::{Users, Groups};

// 用户信息
let users = Users::new_with_refreshed_list();
for user in &users {
    println!("用户: {}", user.name());
    println!("  用户 ID: {}", user.id());
    println!("  组 ID: {}", user.group_id());
}

// 组信息
let groups = Groups::new_with_refreshed_list();
for group in groups.list() {
    println!("组: {} (ID: {})", group.name(), group.id());
}
```

### 9. 系统统计
```rust
// 启动时间和运行时间
println!("启动时间: {}", System::boot_time());
println!("运行时间: {} 秒", System::uptime());

// 负载平均值 (Unix 系统)
let load_avg = System::load_average();
println!("负载平均值: {:.2}, {:.2}, {:.2}", 
         load_avg.one, load_avg.five, load_avg.fifteen);
```

## ⚡ 性能优化建议

### 1. 使用 RefreshKind 精确控制
```rust
use sysinfo::{RefreshKind, MemoryRefreshKind, CpuRefreshKind, ProcessRefreshKind};

// 只刷新需要的信息
let mut sys = System::new_with_specifics(
    RefreshKind::nothing()
        .with_memory(MemoryRefreshKind::nothing().with_ram())
        .with_cpu(CpuRefreshKind::nothing().with_cpu_usage())
        .with_processes(ProcessRefreshKind::nothing().with_memory())
);
```

### 2. 重用 System 实例
```rust
// ✅ 好的做法：重用实例
let mut sys = System::new();
loop {
    sys.refresh_all();
    // 使用 sys...
    std::thread::sleep(Duration::from_secs(1));
}

// ❌ 不好的做法：每次都创建新实例
loop {
    let sys = System::new_all(); // 每次都重新分配内存
    // 使用 sys...
}
```

### 3. 特定刷新方法
```rust
// 只刷新内存
sys.refresh_memory();

// 只刷新 CPU 使用率
sys.refresh_cpu_usage();

// 只刷新特定进程
sys.refresh_processes(ProcessesToUpdate::Some(&[pid]), true);
```

## 🔧 常用工具函数

```rust
// 字节转换
fn bytes_to_gb(bytes: u64) -> f64 {
    bytes as f64 / (1024.0 * 1024.0 * 1024.0)
}

fn bytes_to_mb(bytes: u64) -> f64 {
    bytes as f64 / (1024.0 * 1024.0)
}

// 格式化运行时间
fn format_uptime(seconds: u64) -> String {
    let days = seconds / 86400;
    let hours = (seconds % 86400) / 3600;
    let minutes = (seconds % 3600) / 60;
    format!("{} 天 {} 小时 {} 分钟", days, hours, minutes)
}
```

## 📝 注意事项

1. **系统支持**: 并非所有功能在所有系统上都可用，使用前检查 `IS_SUPPORTED_SYSTEM`
2. **CPU 使用率**: 需要两次测量，建议等待 `MINIMUM_CPU_UPDATE_INTERVAL`
3. **内存单位**: 所有内存相关的值都以字节为单位
4. **性能**: 避免频繁创建新的 System 实例，重用现有实例
5. **权限**: 某些信息可能需要特殊权限才能访问

## 🎯 实际应用场景

- **系统监控**: 监控 CPU、内存、磁盘使用情况
- **进程管理**: 查找、监控特定进程
- **资源分析**: 分析系统资源使用模式
- **性能调优**: 识别系统瓶颈
- **系统信息收集**: 收集系统配置信息

这个指南涵盖了 sysinfo 库的所有主要功能。通过运行示例代码，你可以深入了解每个 API 的使用方法。
