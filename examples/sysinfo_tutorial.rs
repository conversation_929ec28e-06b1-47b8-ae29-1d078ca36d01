// sysinfo 库学习教程
// 运行方式: cargo run --example sysinfo_tutorial

use sysinfo::{System, Disks, Networks, Components, Users, ProcessesToUpdate};
use std::thread;

fn main() {
    println!("🎓 sysinfo 库学习教程");
    println!("{}", "=".repeat(40));
    
    // 第一步：检查系统支持
    basic_system_check();
    
    // 第二步：基本系统信息
    basic_system_info();
    
    // 第三步：内存信息
    memory_info();
    
    // 第四步：CPU 信息
    cpu_info();
    
    // 第五步：进程信息
    process_info();
    
    // 第六步：磁盘信息
    disk_info();
    
    // 第七步：网络信息
    network_info();
    
    // 第八步：其他信息
    other_info();
    
    println!("\n🎉 教程完成！");
}

/// 第一步：基本系统检查
fn basic_system_check() {
    println!("\n📋 第一步：基本系统检查");
    println!("{}", "-".repeat(30));
    
    // 检查系统是否支持
    if sysinfo::IS_SUPPORTED_SYSTEM {
        println!("✅ 当前系统被 sysinfo 支持");
    } else {
        println!("❌ 当前系统不被 sysinfo 支持");
        return;
    }
    
    // 获取当前进程 PID
    match sysinfo::get_current_pid() {
        Ok(pid) => println!("📍 当前进程 PID: {}", pid),
        Err(e) => println!("❌ 无法获取当前进程 PID: {}", e),
    }
}

/// 第二步：基本系统信息
fn basic_system_info() {
    println!("\n🖥️  第二步：基本系统信息");
    println!("{}", "-".repeat(30));
    
    // 这些是静态方法，不需要创建 System 实例
    println!("系统名称: {:?}", System::name().unwrap_or_else(|| "未知".to_string()));
    println!("内核版本: {:?}", System::kernel_version().unwrap_or_else(|| "未知".to_string()));
    println!("操作系统版本: {:?}", System::os_version().unwrap_or_else(|| "未知".to_string()));
    println!("主机名: {:?}", System::host_name().unwrap_or_else(|| "未知".to_string()));
    println!("CPU 架构: {}", System::cpu_arch());
    
    // 物理核心数
    if let Some(cores) = System::physical_core_count() {
        println!("物理核心数: {}", cores);
    }
    
    // 系统启动时间和运行时间
    println!("启动时间: {} (Unix 时间戳)", System::boot_time());
    
    let uptime = System::uptime();
    let hours = uptime / 3600;
    let minutes = (uptime % 3600) / 60;
    println!("运行时间: {} 小时 {} 分钟", hours, minutes);
}

/// 第三步：内存信息
fn memory_info() {
    println!("\n💾 第三步：内存信息");
    println!("{}", "-".repeat(30));
    
    // 创建 System 实例
    let mut sys = System::new();
    
    // 刷新内存信息
    sys.refresh_memory();
    
    // 获取内存信息（单位：字节）
    let total_memory = sys.total_memory();
    let used_memory = sys.used_memory();
    let available_memory = sys.available_memory();
    
    println!("总内存: {:.2} GB", bytes_to_gb(total_memory));
    println!("已用内存: {:.2} GB", bytes_to_gb(used_memory));
    println!("可用内存: {:.2} GB", bytes_to_gb(available_memory));
    
    // 计算内存使用率
    let usage_percent = (used_memory as f64 / total_memory as f64) * 100.0;
    println!("内存使用率: {:.1}%", usage_percent);
    
    // 交换分区信息
    println!("总交换分区: {:.2} GB", bytes_to_gb(sys.total_swap()));
    println!("已用交换分区: {:.2} GB", bytes_to_gb(sys.used_swap()));
}

/// 第四步：CPU 信息
fn cpu_info() {
    println!("\n🖥️  第四步：CPU 信息");
    println!("{}", "-".repeat(30));
    
    let mut sys = System::new();
    
    // 刷新 CPU 信息
    sys.refresh_cpu_all();
    
    println!("CPU 核心数: {}", sys.cpus().len());
    
    // 显示第一个 CPU 的详细信息
    if let Some(cpu) = sys.cpus().first() {
        println!("CPU 品牌: {}", cpu.brand());
        println!("CPU 名称: {}", cpu.name());
        println!("供应商 ID: {}", cpu.vendor_id());
        println!("频率: {} MHz", cpu.frequency());
    }
    
    // 等待一段时间以获取准确的 CPU 使用率
    println!("\n⏳ 等待获取 CPU 使用率...");
    thread::sleep(sysinfo::MINIMUM_CPU_UPDATE_INTERVAL);
    
    // 再次刷新以获取使用率
    sys.refresh_cpu_usage();
    
    println!("全局 CPU 使用率: {:.1}%", sys.global_cpu_usage());
    
    // 显示前 4 个核心的使用率
    println!("各核心使用率 (前4个):");
    for (i, cpu) in sys.cpus().iter().enumerate().take(4) {
        println!("  核心 {}: {:.1}%", i, cpu.cpu_usage());
    }
}

/// 第五步：进程信息
fn process_info() {
    println!("\n🔄 第五步：进程信息");
    println!("{}", "-".repeat(30));
    
    let mut sys = System::new();
    
    // 刷新所有进程信息
    sys.refresh_processes(ProcessesToUpdate::All, true);
    
    println!("总进程数: {}", sys.processes().len());
    
    // 显示前 3 个进程
    println!("\n前 3 个进程:");
    for (i, (pid, process)) in sys.processes().iter().enumerate().take(3) {
        println!("进程 {}: {} (PID: {})", i + 1, process.name().to_string_lossy(), pid);
        println!("  内存使用: {:.2} MB", bytes_to_mb(process.memory()));
        println!("  CPU 使用率: {:.1}%", process.cpu_usage());
        println!("  状态: {:?}", process.status());
    }
    
    // 查找当前进程
    if let Ok(current_pid) = sysinfo::get_current_pid() {
        if let Some(current_process) = sys.process(current_pid) {
            println!("\n📍 当前进程信息:");
            println!("  名称: {}", current_process.name().to_string_lossy());
            println!("  内存使用: {:.2} MB", bytes_to_mb(current_process.memory()));
            println!("  运行时间: {} 秒", current_process.run_time());
        }
    }
}

/// 第六步：磁盘信息
fn disk_info() {
    println!("\n💿 第六步：磁盘信息");
    println!("{}", "-".repeat(30));
    
    let disks = Disks::new_with_refreshed_list();
    
    println!("磁盘数量: {}", disks.len());
    
    for (i, disk) in disks.iter().enumerate().take(3) {
        println!("\n磁盘 {}:", i + 1);
        println!("  名称: {:?}", disk.name());
        println!("  挂载点: {:?}", disk.mount_point());
        println!("  文件系统: {:?}", disk.file_system());
        println!("  总容量: {:.2} GB", bytes_to_gb(disk.total_space()));
        println!("  可用空间: {:.2} GB", bytes_to_gb(disk.available_space()));
        
        // 计算使用率
        let used = disk.total_space() - disk.available_space();
        let usage = if disk.total_space() > 0 {
            (used as f64 / disk.total_space() as f64) * 100.0
        } else {
            0.0
        };
        println!("  使用率: {:.1}%", usage);
    }
}

/// 第七步：网络信息
fn network_info() {
    println!("\n🌐 第七步：网络信息");
    println!("{}", "-".repeat(30));
    
    let networks = Networks::new_with_refreshed_list();
    
    println!("网络接口数量: {}", networks.len());
    
    for (i, (interface_name, data)) in networks.iter().enumerate().take(3) {
        println!("\n接口 {}: {}", i + 1, interface_name);
        println!("  MAC 地址: {}", data.mac_address());
        println!("  接收数据: {:.2} MB", bytes_to_mb(data.total_received()));
        println!("  发送数据: {:.2} MB", bytes_to_mb(data.total_transmitted()));
        println!("  接收包数: {}", data.total_packets_received());
        println!("  发送包数: {}", data.total_packets_transmitted());
    }
}

/// 第八步：其他信息
fn other_info() {
    println!("\n🌡️  第八步：其他信息");
    println!("{}", "-".repeat(30));
    
    // 组件温度信息
    let components = Components::new_with_refreshed_list();
    if !components.is_empty() {
        println!("温度传感器数量: {}", components.len());
        for (i, component) in components.iter().enumerate().take(3) {
            let temp = component.temperature().map(|t| format!("{:.1}°C", t))
                .unwrap_or_else(|| "N/A".to_string());
            println!("  传感器 {}: {} - {}",
                     i + 1, component.label(), temp);
        }
    } else {
        println!("未检测到温度传感器");
    }
    
    // 用户信息
    let users = Users::new_with_refreshed_list();
    println!("\n系统用户数量: {}", users.len());
    
    // 负载平均值
    let load_avg = System::load_average();
    println!("\n负载平均值:");
    println!("  1 分钟: {:.2}", load_avg.one);
    println!("  5 分钟: {:.2}", load_avg.five);
    println!("  15 分钟: {:.2}", load_avg.fifteen);
}

/// 工具函数：字节转 GB
fn bytes_to_gb(bytes: u64) -> f64 {
    bytes as f64 / (1024.0 * 1024.0 * 1024.0)
}

/// 工具函数：字节转 MB
fn bytes_to_mb(bytes: u64) -> f64 {
    bytes as f64 / (1024.0 * 1024.0)
}
