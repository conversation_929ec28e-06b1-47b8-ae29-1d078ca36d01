use sysinfo::{
    Components, Disks, Networks, System, Users, Groups,
    ProcessRefreshKind, RefreshKind, MemoryRefreshKind, CpuRefreshKind,
    ProcessesToUpdate, Signal, Pid
};
use std::thread;
use std::time::Duration;

pub fn run_sysinfo_demo() {
    println!("🚀 sysinfo 库完整使用示例");
    println!("{}", "=".repeat(50));
    
    // 检查系统是否支持
    if !sysinfo::IS_SUPPORTED_SYSTEM {
        println!("❌ 当前系统不被支持");
        return;
    }
    
    // 演示各种功能
    demo_system_info();
    demo_memory_info();
    demo_cpu_info();
    demo_process_info();
    demo_disk_info();
    demo_network_info();
    demo_component_info();
    demo_user_info();
    demo_system_stats();
    demo_advanced_usage();
}

/// 演示系统基本信息
fn demo_system_info() {
    println!("\n📋 系统基本信息");
    println!("{}", "-".repeat(30));
    
    // 获取系统信息（静态方法，不需要实例）
    println!("系统名称: {:?}", System::name().unwrap_or_else(|| "未知".to_string()));
    println!("内核版本: {:?}", System::kernel_version().unwrap_or_else(|| "未知".to_string()));
    println!("操作系统版本: {:?}", System::os_version().unwrap_or_else(|| "未知".to_string()));
    println!("操作系统详细版本: {:?}", System::long_os_version().unwrap_or_else(|| "未知".to_string()));
    println!("主机名: {:?}", System::host_name().unwrap_or_else(|| "未知".to_string()));
    println!("CPU 架构: {}", System::cpu_arch());
    
    // 物理核心数
    match System::physical_core_count() {
        Some(count) => println!("物理核心数: {}", count),
        None => println!("物理核心数: 无法获取"),
    }
    
    // 文件描述符限制
    match System::open_files_limit() {
        Some(limit) => println!("文件描述符限制: {}", limit),
        None => println!("文件描述符限制: 无法获取"),
    }
}

/// 演示内存信息
fn demo_memory_info() {
    println!("\n💾 内存信息");
    println!("{}", "-".repeat(30));
    
    // 创建系统实例并刷新内存信息
    let mut sys = System::new();
    sys.refresh_memory();
    
    // 内存信息（以字节为单位）
    println!("总内存: {:.2} GB", bytes_to_gb(sys.total_memory()));
    println!("已用内存: {:.2} GB", bytes_to_gb(sys.used_memory()));
    println!("可用内存: {:.2} GB", bytes_to_gb(sys.available_memory()));
    println!("空闲内存: {:.2} GB", bytes_to_gb(sys.free_memory()));
    
    // 交换分区信息
    println!("总交换分区: {:.2} GB", bytes_to_gb(sys.total_swap()));
    println!("已用交换分区: {:.2} GB", bytes_to_gb(sys.used_swap()));
    println!("空闲交换分区: {:.2} GB", bytes_to_gb(sys.free_swap()));
    
    // 内存使用率
    let memory_usage = (sys.used_memory() as f64 / sys.total_memory() as f64) * 100.0;
    println!("内存使用率: {:.1}%", memory_usage);
    
    // 演示特定内存刷新
    println!("\n🔄 演示特定内存信息刷新:");
    sys.refresh_memory_specifics(MemoryRefreshKind::nothing().with_ram());
    println!("仅刷新 RAM 后 - 已用内存: {:.2} GB", bytes_to_gb(sys.used_memory()));
}

/// 演示 CPU 信息
fn demo_cpu_info() {
    println!("\n🖥️  CPU 信息");
    println!("{}", "-".repeat(30));
    
    let mut sys = System::new();
    
    // 首次刷新 CPU 信息
    sys.refresh_cpu_all();
    
    println!("CPU 核心数: {}", sys.cpus().len());
    
    // 显示每个 CPU 核心的基本信息
    for (i, cpu) in sys.cpus().iter().enumerate() {
        println!("CPU {}: {} - {} MHz", i, cpu.name(), cpu.frequency());
        if i == 0 {
            println!("  品牌: {}", cpu.brand());
            println!("  供应商 ID: {}", cpu.vendor_id());
        }
    }
    
    // 等待一段时间以获取准确的 CPU 使用率
    println!("\n⏳ 等待 {} 毫秒以获取准确的 CPU 使用率...", 
             sysinfo::MINIMUM_CPU_UPDATE_INTERVAL.as_millis());
    thread::sleep(sysinfo::MINIMUM_CPU_UPDATE_INTERVAL);
    
    // 再次刷新以获取使用率
    sys.refresh_cpu_usage();
    
    println!("全局 CPU 使用率: {:.1}%", sys.global_cpu_usage());
    
    // 显示每个核心的使用率
    println!("各核心使用率:");
    for (i, cpu) in sys.cpus().iter().enumerate() {
        println!("  CPU {}: {:.1}%", i, cpu.cpu_usage());
    }
    
    // 演示不同的 CPU 刷新方式
    println!("\n🔄 演示不同的 CPU 刷新方式:");
    sys.refresh_cpu_specifics(CpuRefreshKind::nothing().with_frequency());
    println!("仅刷新频率信息完成");
}

/// 字节转换为 GB
fn bytes_to_gb(bytes: u64) -> f64 {
    bytes as f64 / (1024.0 * 1024.0 * 1024.0)
}

/// 字节转换为 MB
fn bytes_to_mb(bytes: u64) -> f64 {
    bytes as f64 / (1024.0 * 1024.0)
}

/// 演示进程信息
fn demo_process_info() {
    println!("\n🔄 进程信息");
    println!("{}", "-".repeat(30));

    let mut sys = System::new();

    // 刷新进程信息
    sys.refresh_processes(ProcessesToUpdate::All, true);

    println!("总进程数: {}", sys.processes().len());

    // 显示前 5 个进程的详细信息
    println!("\n前 5 个进程详情:");
    for (i, (pid, process)) in sys.processes().iter().enumerate() {
        if i >= 5 { break; }

        println!("进程 {}: {} (PID: {})", i + 1, process.name().to_string_lossy(), pid);
        println!("  状态: {:?}", process.status());
        println!("  CPU 使用率: {:.1}%", process.cpu_usage());
        println!("  内存使用: {:.2} MB", bytes_to_mb(process.memory()));
        println!("  虚拟内存: {:.2} MB", bytes_to_mb(process.virtual_memory()));

        // 磁盘使用情况
        let disk_usage = process.disk_usage();
        println!("  磁盘读取: {:.2} MB", bytes_to_mb(disk_usage.total_read_bytes));
        println!("  磁盘写入: {:.2} MB", bytes_to_mb(disk_usage.total_written_bytes));

        // 运行时间
        println!("  运行时间: {} 秒", process.run_time());
        println!();
    }
    
    // 按名称查找进程
    println!("🔍 查找特定进程 (以 'rust' 开头的进程):");
    let rust_processes: Vec<_> = sys.processes_by_name("rust".as_ref()).collect();
    if rust_processes.is_empty() {
        println!("  未找到以 'rust' 开头的进程");
    } else {
        for process in rust_processes.iter().take(3) {
            println!("  找到: {} (PID: {})", 
                     process.name().to_string_lossy(), 
                     process.pid());
        }
    }
    
    // 获取当前进程信息
    if let Ok(current_pid) = sysinfo::get_current_pid() {
        println!("\n📍 当前进程信息 (PID: {}):", current_pid);
        if let Some(current_process) = sys.process(current_pid) {
            println!("  名称: {}", current_process.name().to_string_lossy());
            println!("  内存使用: {:.2} MB", bytes_to_mb(current_process.memory()));
            println!("  CPU 使用率: {:.1}%", current_process.cpu_usage());
        }
    }
}

/// 演示磁盘信息
fn demo_disk_info() {
    println!("\n💿 磁盘信息");
    println!("{}", "-".repeat(30));
    
    let disks = Disks::new_with_refreshed_list();
    
    println!("磁盘数量: {}", disks.len());
    
    for (i, disk) in disks.iter().enumerate() {
        println!("\n磁盘 {}:", i + 1);
        println!("  名称: {:?}", disk.name());
        println!("  挂载点: {:?}", disk.mount_point());
        println!("  文件系统: {:?}", disk.file_system());
        println!("  类型: {:?}", disk.kind());
        println!("  总容量: {:.2} GB", bytes_to_gb(disk.total_space()));
        println!("  可用空间: {:.2} GB", bytes_to_gb(disk.available_space()));
        
        // 计算使用率
        let used_space = disk.total_space() - disk.available_space();
        let usage_percent = if disk.total_space() > 0 {
            (used_space as f64 / disk.total_space() as f64) * 100.0
        } else {
            0.0
        };
        println!("  使用率: {:.1}%", usage_percent);
        println!("  是否可移除: {}", disk.is_removable());
    }
}

/// 演示网络信息
fn demo_network_info() {
    println!("\n🌐 网络信息");
    println!("{}", "-".repeat(30));
    
    let networks = Networks::new_with_refreshed_list();
    
    println!("网络接口数量: {}", networks.len());
    
    for (interface_name, data) in &networks {
        println!("\n接口: {}", interface_name);
        println!("  MAC 地址: {}", data.mac_address());
        println!("  接收数据: {:.2} MB (总计)", bytes_to_mb(data.total_received()));
        println!("  发送数据: {:.2} MB (总计)", bytes_to_mb(data.total_transmitted()));
        println!("  接收数据: {:.2} MB (本次)", bytes_to_mb(data.received()));
        println!("  发送数据: {:.2} MB (本次)", bytes_to_mb(data.transmitted()));
        println!("  接收包数: {}", data.total_packets_received());
        println!("  发送包数: {}", data.total_packets_transmitted());
        println!("  接收错误: {}", data.total_errors_on_received());
        println!("  发送错误: {}", data.total_errors_on_transmitted());
    }
}

/// 演示组件温度信息
fn demo_component_info() {
    println!("\n🌡️  组件温度信息");
    println!("{}", "-".repeat(30));

    let components = Components::new_with_refreshed_list();

    if components.is_empty() {
        println!("未检测到温度传感器或当前系统不支持温度监控");
        return;
    }

    println!("温度传感器数量: {}", components.len());

    for (i, component) in components.iter().enumerate() {
        println!("\n传感器 {}:", i + 1);
        println!("  标签: {}", component.label());
        let temp = component.temperature().map(|t| format!("{:.1}°C", t))
            .unwrap_or_else(|| "N/A".to_string());
        println!("  当前温度: {}", temp);

        if let Some(max_temp) = component.max() {
            println!("  最高温度: {:.1}°C", max_temp);
        }

        if let Some(critical_temp) = component.critical() {
            println!("  临界温度: {:.1}°C", critical_temp);
        }
    }
}

/// 演示用户信息
fn demo_user_info() {
    println!("\n👥 用户信息");
    println!("{}", "-".repeat(30));

    let users = Users::new_with_refreshed_list();

    println!("系统用户数量: {}", users.len());

    // 显示前 5 个用户
    for (i, user) in users.iter().enumerate() {
        if i >= 5 { break; }

        println!("\n用户 {}:", i + 1);
        println!("  用户名: {}", user.name());
        println!("  用户 ID: {:?}", user.id());
        println!("  组 ID: {:?}", user.group_id());

        // 显示用户所属的组
        let groups: Vec<String> = user.groups().iter().map(|g| g.name().to_string()).collect();
        if !groups.is_empty() {
            println!("  所属组: {}", groups.join(", "));
        }
    }

    // 显示系统组信息
    println!("\n📋 系统组信息:");
    let groups = Groups::new_with_refreshed_list();
    println!("系统组数量: {}", groups.len());

    // 显示前 5 个组
    for (i, group) in groups.list().iter().enumerate() {
        if i >= 5 { break; }
        println!("  组 {}: {} (ID: {:?})", i + 1, group.name(), group.id());
    }
}

/// 演示系统统计信息
fn demo_system_stats() {
    println!("\n📊 系统统计信息");
    println!("{}", "-".repeat(30));

    // 启动时间
    let boot_time = System::boot_time();
    println!("系统启动时间: {} (Unix 时间戳)", boot_time);

    // 运行时间
    let uptime = System::uptime();
    let days = uptime / 86400;
    let hours = (uptime % 86400) / 3600;
    let minutes = (uptime % 3600) / 60;
    let seconds = uptime % 60;

    println!("系统运行时间: {} 天 {} 小时 {} 分钟 {} 秒", days, hours, minutes, seconds);
    println!("系统运行时间 (总秒数): {} 秒", uptime);

    // 负载平均值 (仅在 Unix 系统上可用)
    let load_avg = System::load_average();
    println!("\n负载平均值:");
    println!("  1 分钟: {:.2}", load_avg.one);
    println!("  5 分钟: {:.2}", load_avg.five);
    println!("  15 分钟: {:.2}", load_avg.fifteen);

    // 当前进程 PID
    match sysinfo::get_current_pid() {
        Ok(pid) => println!("\n当前进程 PID: {}", pid),
        Err(e) => println!("\n无法获取当前进程 PID: {}", e),
    }
}

/// 演示高级用法
fn demo_advanced_usage() {
    println!("\n🚀 高级用法演示");
    println!("{}", "-".repeat(30));

    // 1. 使用 RefreshKind 进行精确控制
    println!("1. 精确控制刷新内容:");
    let mut sys = System::new_with_specifics(
        RefreshKind::nothing()
            .with_memory(MemoryRefreshKind::nothing().with_ram())
            .with_cpu(CpuRefreshKind::nothing().with_cpu_usage())
            .with_processes(ProcessRefreshKind::nothing().with_memory())
    );

    println!("   仅刷新了 RAM、CPU 使用率和进程内存信息");
    println!("   总内存: {:.2} GB", bytes_to_gb(sys.total_memory()));

    // 2. 进程监控示例
    println!("\n2. 进程监控示例:");
    monitor_process_example(&mut sys);

    // 3. 系统信息序列化 (如果启用了 serde 功能)
    println!("\n3. 系统信息序列化:");
    #[cfg(feature = "serde")]
    {
        match serde_json::to_string_pretty(&sys) {
            Ok(json) => println!("   系统信息已序列化为 JSON (长度: {} 字符)", json.len()),
            Err(e) => println!("   序列化失败: {}", e),
        }
    }
    #[cfg(not(feature = "serde"))]
    {
        println!("   serde 功能未启用，无法演示序列化");
    }

    // 4. 支持的信号列表
    println!("\n4. 系统支持的信号:");
    for (i, signal) in sysinfo::SUPPORTED_SIGNALS.iter().enumerate() {
        if i >= 5 {
            println!("   ... 还有 {} 个信号", sysinfo::SUPPORTED_SIGNALS.len() - 5);
            break;
        }
        println!("   {}: {:?}", i + 1, signal);
    }
}

/// 进程监控示例
fn monitor_process_example(sys: &mut System) {
    println!("   监控当前进程的资源使用情况...");

    if let Ok(current_pid) = sysinfo::get_current_pid() {
        // 刷新特定进程
        sys.refresh_processes(ProcessesToUpdate::Some(&[current_pid]), true);

        if let Some(process) = sys.process(current_pid) {
            println!("   进程名: {}", process.name().to_string_lossy());
            println!("   内存使用: {:.2} MB", bytes_to_mb(process.memory()));
            println!("   虚拟内存: {:.2} MB", bytes_to_mb(process.virtual_memory()));
            println!("   CPU 使用率: {:.1}%", process.cpu_usage());

            // 父进程信息
            if let Some(parent_pid) = process.parent() {
                println!("   父进程 PID: {}", parent_pid);
                if let Some(parent) = sys.process(parent_pid) {
                    println!("   父进程名: {}", parent.name().to_string_lossy());
                }
            }

            // 环境变量 (部分)
            let env_vars: Vec<_> = process.environ().iter().take(3).collect();
            if !env_vars.is_empty() {
                println!("   环境变量 (前3个):");
                for env_var in env_vars {
                    let env_str = env_var.to_string_lossy();
                    if let Some(eq_pos) = env_str.find('=') {
                        let (key, value) = env_str.split_at(eq_pos);
                        let value = &value[1..]; // 跳过 '='
                        println!("     {}={}", key,
                                if value.len() > 50 {
                                    format!("{}...", &value[..47])
                                } else {
                                    value.to_string()
                                });
                    }
                }
            }
        }
    }
}
