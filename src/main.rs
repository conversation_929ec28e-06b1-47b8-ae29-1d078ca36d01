use std::process::Command;
use std::str;

fn get_mac_serial() -> String {
    let output = Command::new("ioreg")
        .arg("-l")
        .output()
        .expect("Failed to execute ioreg");
    let stdout = str::from_utf8(&output.stdout).unwrap();
    let serial_line = stdout.lines().find(|line| line.contains("IOPlatformSerialNumber")).unwrap_or("Not found");
    serial_line.split("\"").nth(3).unwrap_or("Unknown").trim().to_string()
}

fn main() {
    println!("Mac Serial Number: {}", get_mac_serial());
}