use std::process::Command;
use std::str;
use std::env;

mod sysinfo_demo;

fn get_mac_serial() -> String {
    let output = Command::new("ioreg")
        .arg("-l")
        .output()
        .expect("Failed to execute ioreg");
    let stdout = str::from_utf8(&output.stdout).unwrap();
    let serial_line = stdout.lines().find(|line| line.contains("IOPlatformSerialNumber")).unwrap_or("Not found");
    serial_line.split("\"").nth(3).unwrap_or("Unknown").trim().to_string()
}

fn main() {
    let args: Vec<String> = env::args().collect();

    if args.len() > 1 && args[1] == "sysinfo" {
        // 运行 sysinfo 演示
        sysinfo_demo::run_sysinfo_demo();
    } else {
        // 默认行为：显示 Mac 序列号
        println!("Mac Serial Number: {}", get_mac_serial());
        println!("\n💡 提示: 运行 'cargo run sysinfo' 来查看 sysinfo 库的完整演示");
    }
}