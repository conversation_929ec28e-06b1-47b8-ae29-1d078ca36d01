# sysinfo 库 API 快速参考

## 🚀 快速开始

```rust
use sysinfo::{System, Disks, Networks, Components, Users};

// 创建系统实例
let mut sys = System::new_all();  // 加载所有信息
// 或者
let mut sys = System::new();      // 创建空实例，需要手动刷新
```

## 📋 系统基本信息 (静态方法)

```rust
// 系统信息
System::name()                    // -> Option<String>     系统名称
System::kernel_version()          // -> Option<String>     内核版本
System::os_version()              // -> Option<String>     操作系统版本
System::long_os_version()         // -> Option<String>     详细操作系统版本
System::host_name()               // -> Option<String>     主机名
System::cpu_arch()                // -> String             CPU 架构

// 硬件信息
System::physical_core_count()     // -> Option<usize>      物理核心数
System::open_files_limit()        // -> Option<usize>      文件描述符限制

// 时间信息
System::boot_time()               // -> u64                启动时间 (Unix 时间戳)
System::uptime()                  // -> u64                运行时间 (秒)
System::load_average()            // -> LoadAvg            负载平均值

// 当前进程
sysinfo::get_current_pid()        // -> Result<Pid, _>     当前进程 PID
```

## 💾 内存信息

```rust
let mut sys = System::new();
sys.refresh_memory();  // 刷新内存信息

// 内存信息 (字节)
sys.total_memory()      // -> u64    总内存
sys.used_memory()       // -> u64    已用内存
sys.available_memory()  // -> u64    可用内存
sys.free_memory()       // -> u64    空闲内存

// 交换分区 (字节)
sys.total_swap()        // -> u64    总交换分区
sys.used_swap()         // -> u64    已用交换分区
sys.free_swap()         // -> u64    空闲交换分区
```

## 🖥️ CPU 信息

```rust
let mut sys = System::new();
sys.refresh_cpu_all();  // 刷新所有 CPU 信息

// 基本信息
sys.cpus().len()                    // CPU 核心数
sys.global_cpu_usage()              // 全局 CPU 使用率

// 遍历每个 CPU 核心
for cpu in sys.cpus() {
    cpu.name()          // -> &str       CPU 名称
    cpu.brand()         // -> &str       CPU 品牌
    cpu.vendor_id()     // -> &str       供应商 ID
    cpu.frequency()     // -> u64        频率 (MHz)
    cpu.cpu_usage()     // -> f32        使用率 (%)
}

// 不同的刷新方法
sys.refresh_cpu_usage()             // 仅刷新使用率
sys.refresh_cpu_frequency()         // 仅刷新频率
```

## 🔄 进程信息

```rust
let mut sys = System::new();
sys.refresh_processes(ProcessesToUpdate::All, true);

// 基本信息
sys.processes().len()               // 进程总数

// 遍历所有进程
for (pid, process) in sys.processes() {
    process.name()                  // -> &OsStr         进程名称
    process.pid()                   // -> Pid            进程 ID
    process.parent()                // -> Option<Pid>    父进程 ID
    process.status()                // -> ProcessStatus  进程状态
    process.memory()                // -> u64            内存使用 (字节)
    process.virtual_memory()        // -> u64            虚拟内存 (字节)
    process.cpu_usage()             // -> f32            CPU 使用率 (%)
    process.run_time()              // -> u64            运行时间 (秒)
    process.disk_usage()            // -> DiskUsage      磁盘使用情况
    process.environ()               // -> &[OsString]    环境变量
}

// 查找进程
sys.process(pid)                    // -> Option<&Process>  根据 PID 查找
sys.processes_by_name("name")       // -> impl Iterator     根据名称查找
sys.processes_by_exact_name("name") // -> impl Iterator     精确名称匹配
```

## 💿 磁盘信息

```rust
let disks = Disks::new_with_refreshed_list();

for disk in &disks {
    disk.name()             // -> &OsStr         磁盘名称
    disk.mount_point()      // -> &Path          挂载点
    disk.file_system()      // -> &OsStr         文件系统
    disk.kind()             // -> DiskKind       磁盘类型 (SSD/HDD/Unknown)
    disk.total_space()      // -> u64            总容量 (字节)
    disk.available_space()  // -> u64            可用空间 (字节)
    disk.is_removable()     // -> bool           是否可移除
}
```

## 🌐 网络信息

```rust
let networks = Networks::new_with_refreshed_list();

for (interface_name, data) in &networks {
    data.mac_address()              // -> MacAddr        MAC 地址
    data.total_received()           // -> u64            总接收字节数
    data.total_transmitted()        // -> u64            总发送字节数
    data.received()                 // -> u64            本次接收字节数
    data.transmitted()              // -> u64            本次发送字节数
    data.total_packets_received()   // -> u64            总接收包数
    data.total_packets_transmitted() // -> u64           总发送包数
    data.total_errors_on_received() // -> u64            接收错误数
    data.total_errors_on_transmitted() // -> u64         发送错误数
}
```

## 🌡️ 组件温度

```rust
let components = Components::new_with_refreshed_list();

for component in &components {
    component.label()       // -> &str           组件标签
    component.temperature() // -> Option<f32>    当前温度 (°C)
    component.max()         // -> Option<f32>    最高温度 (°C)
    component.critical()    // -> Option<f32>    临界温度 (°C)
}
```

## 👥 用户和组信息

```rust
// 用户信息
let users = Users::new_with_refreshed_list();
for user in &users {
    user.name()         // -> &str           用户名
    user.id()           // -> &Uid           用户 ID
    user.group_id()     // -> Gid            组 ID
    user.groups()       // -> &[Group]       所属组列表
}

// 组信息
let groups = Groups::new_with_refreshed_list();
for group in groups.list() {
    group.name()        // -> &str           组名
    group.id()          // -> Gid            组 ID
}
```

## ⚙️ 精确控制刷新

```rust
use sysinfo::{RefreshKind, MemoryRefreshKind, CpuRefreshKind, ProcessRefreshKind};

// 创建时指定刷新内容
let mut sys = System::new_with_specifics(
    RefreshKind::nothing()
        .with_memory(MemoryRefreshKind::everything())
        .with_cpu(CpuRefreshKind::everything())
        .with_processes(ProcessRefreshKind::everything())
);

// 运行时精确刷新
sys.refresh_specifics(
    RefreshKind::nothing()
        .with_memory(MemoryRefreshKind::nothing().with_ram())
        .with_cpu(CpuRefreshKind::nothing().with_cpu_usage())
);

// 特定内存刷新
sys.refresh_memory_specifics(MemoryRefreshKind::nothing().with_ram());

// 特定 CPU 刷新
sys.refresh_cpu_specifics(CpuRefreshKind::nothing().with_frequency());

// 特定进程刷新
sys.refresh_processes_specifics(
    ProcessesToUpdate::Some(&[pid1, pid2]),
    true,
    ProcessRefreshKind::nothing().with_memory()
);
```

## 🔧 常用工具函数

```rust
// 单位转换
fn bytes_to_gb(bytes: u64) -> f64 {
    bytes as f64 / (1024.0 * 1024.0 * 1024.0)
}

fn bytes_to_mb(bytes: u64) -> f64 {
    bytes as f64 / (1024.0 * 1024.0)
}

// 格式化运行时间
fn format_uptime(seconds: u64) -> String {
    let days = seconds / 86400;
    let hours = (seconds % 86400) / 3600;
    let minutes = (seconds % 3600) / 60;
    format!("{} 天 {} 小时 {} 分钟", days, hours, minutes)
}

// 计算使用率
fn calculate_usage_percent(used: u64, total: u64) -> f64 {
    if total > 0 {
        (used as f64 / total as f64) * 100.0
    } else {
        0.0
    }
}
```

## 📊 枚举类型

```rust
// 进程状态
enum ProcessStatus {
    Idle,
    Run,
    Sleep,
    Stop,
    Zombie,
    Tracing,
    Dead,
    Wakekill,
    Waking,
    Parked,
    LockBlocked,
    UninterruptibleDiskSleep,
    Unknown(u32),
}

// 磁盘类型
enum DiskKind {
    HDD,        // 机械硬盘
    SSD,        // 固态硬盘
    Unknown(i32),
}

// 信号 (Unix 系统)
enum Signal {
    Hangup,
    Interrupt,
    Quit,
    Illegal,
    Trap,
    Abort,
    // ... 更多信号
}
```

## ⚠️ 重要注意事项

1. **CPU 使用率**: 需要两次测量才能准确，建议等待 `MINIMUM_CPU_UPDATE_INTERVAL`
2. **内存单位**: 所有内存值都以字节为单位
3. **系统支持**: 检查 `IS_SUPPORTED_SYSTEM` 常量
4. **性能**: 重用 System 实例而不是频繁创建新实例
5. **权限**: 某些信息可能需要特殊权限
6. **平台差异**: 不同操作系统支持的功能可能不同

## 🎯 最佳实践

```rust
// ✅ 好的做法
let mut sys = System::new();
loop {
    sys.refresh_specifics(RefreshKind::nothing().with_memory(MemoryRefreshKind::everything()));
    // 使用数据...
    std::thread::sleep(Duration::from_secs(1));
}

// ❌ 避免的做法
loop {
    let sys = System::new_all(); // 每次都重新分配内存
    // 使用数据...
}
```
